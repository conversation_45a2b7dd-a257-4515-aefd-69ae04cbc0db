/****************************************************************************************
 * main_split_radix_simd_mpi.cc - Split-Radix NTT的MPI+SIMD并行实现
 *
 * Split-Radix算法特点：
 * 1. 结合Radix-2和Radix-4的优势，减少乘法运算数量
 * 2. 理论复杂度：4n log n - 6n + 8 次乘法（比标准Radix-2少约25%）
 * 3. 更好的缓存局部性和并行性
 * 4. 适合大规模数据的分布式计算
 *
 * 并行策略：
 * - MPI进程级：分布式Split-Radix计算
 * - SIMD指令级：向量化的蝶形运算
 * - 智能负载均衡：动态任务分配
 *
 * 编译：mpicxx -O3 -std=c++17 -march=native main_split_radix_simd_mpi.cc -o split_radix_simd_mpi
 * 运行：mpirun -np 4 ./split_radix_simd_mpi
 ****************************************************************************************/
#include <bits/stdc++.h>
#include <mpi.h>
#include <chrono>
#include <iomanip>

#ifdef __ARM_NEON
#include <arm_neon.h>
#endif

using namespace std;

/* ============================== I/O 函数 ============================== */
void fRead(int *a, int *b, int *n, int *p, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".in";
    ifstream fin(path);
    if(!fin) { 
        cerr << "无法打开输入文件: " << path << '\n'; 
        MPI_Abort(MPI_COMM_WORLD, 1); 
    }
    fin >> *n >> *p;
    for (int i = 0; i < *n; ++i) fin >> a[i];
    for (int i = 0; i < *n; ++i) fin >> b[i];
}

void fCheck(int *ab, int n, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".out";
    ifstream fin(path);
    if(!fin) { 
        cerr << "无法打开输出文件: " << path << '\n'; 
        MPI_Abort(MPI_COMM_WORLD, 1); 
    }
    for (int i = 0; i < 2 * n - 1; ++i) {
        int x; 
        fin >> x;
        if (x != ab[i]) { 
            cout << "多项式乘法结果错误 (id="<<input_id<<")\n"; 
            return; 
        }
    }
    cout << "多项式乘法结果正确 (id="<<input_id<<")\n";
}

void fWrite(int *ab, int n, int input_id) {
    string path = "files/" + to_string(input_id) + ".out";
    ofstream fout(path);
    for (int i = 0; i < 2 * n - 1; ++i) fout << ab[i] << '\n';
}

/* ============================== Barrett规约器 ============================== */
class SplitRadixBarrett {
public:
    unsigned int mod;
    uint64_t inv;
    
    explicit SplitRadixBarrett(unsigned int m = 1) : mod(m) {
        inv = (static_cast<__uint128_t>(1) << 64) / m;
    }
    
    inline unsigned int reduce(uint64_t a) const {
        uint64_t q = (static_cast<__uint128_t>(a) * inv) >> 64;
        uint64_t r = a - q * mod;
        return static_cast<unsigned int>(r >= mod ? r - mod : r);
    }
    
    inline unsigned int mul(unsigned int a, unsigned int b) const {
        return reduce(static_cast<uint64_t>(a) * b);
    }
    
    inline unsigned int add(unsigned int a, unsigned int b) const {
        unsigned int s = a + b;
        return s >= mod ? s - mod : s;
    }
    
    inline unsigned int sub(unsigned int a, unsigned int b) const {
        return a >= b ? a - b : a + mod - b;
    }

#ifdef __ARM_NEON
    /**
     * @brief NEON优化的Split-Radix蝶形运算
     */
    inline void split_radix_butterfly_neon(uint32x4_t& a, uint32x4_t& b, uint32x4_t& c, uint32x4_t& d,
                                          uint32x4_t w1, uint32x4_t w3) const {
        uint32x4_t mod_vec = vdupq_n_u32(mod);
        
        // Split-Radix蝶形运算的SIMD实现
        // t1 = a + c, t2 = a - c
        uint32x4_t t1 = vaddq_u32(a, c);
        uint32x4_t mask1 = vcgeq_u32(t1, mod_vec);
        t1 = vbslq_u32(mask1, vsubq_u32(t1, mod_vec), t1);
        
        uint32x4_t t2 = vsubq_u32(a, c);
        uint32x4_t mask2 = vcltq_u32(a, c);
        t2 = vbslq_u32(mask2, vaddq_u32(t2, mod_vec), t2);
        
        // t3 = b + d, t4 = (b - d) * w1
        uint32x4_t t3 = vaddq_u32(b, d);
        uint32x4_t mask3 = vcgeq_u32(t3, mod_vec);
        t3 = vbslq_u32(mask3, vsubq_u32(t3, mod_vec), t3);
        
        uint32x4_t bd_diff = vsubq_u32(b, d);
        uint32x4_t mask4 = vcltq_u32(b, d);
        bd_diff = vbslq_u32(mask4, vaddq_u32(bd_diff, mod_vec), bd_diff);
        uint32x4_t t4 = mul_neon(bd_diff, w1);
        
        // 输出：a = t1 + t3, b = t2 + t4, c = t1 - t3, d = (t2 - t4) * w3
        a = vaddq_u32(t1, t3);
        uint32x4_t mask5 = vcgeq_u32(a, mod_vec);
        a = vbslq_u32(mask5, vsubq_u32(a, mod_vec), a);
        
        b = vaddq_u32(t2, t4);
        uint32x4_t mask6 = vcgeq_u32(b, mod_vec);
        b = vbslq_u32(mask6, vsubq_u32(b, mod_vec), b);
        
        c = vsubq_u32(t1, t3);
        uint32x4_t mask7 = vcltq_u32(t1, t3);
        c = vbslq_u32(mask7, vaddq_u32(c, mod_vec), c);
        
        uint32x4_t t2_t4_diff = vsubq_u32(t2, t4);
        uint32x4_t mask8 = vcltq_u32(t2, t4);
        t2_t4_diff = vbslq_u32(mask8, vaddq_u32(t2_t4_diff, mod_vec), t2_t4_diff);
        d = mul_neon(t2_t4_diff, w3);
    }
    
    /**
     * @brief NEON优化的Barrett模乘法
     */
    inline uint32x4_t mul_neon(uint32x4_t a_vec, uint32x4_t b_vec) const {
        uint32x2_t a_lo = vget_low_u32(a_vec);
        uint32x2_t a_hi = vget_high_u32(a_vec);
        uint32x2_t b_lo = vget_low_u32(b_vec);
        uint32x2_t b_hi = vget_high_u32(b_vec);
        
        uint64x2_t x_lo = vmull_u32(a_lo, b_lo);
        uint64x2_t x_hi = vmull_u32(a_hi, b_hi);
        
        // 使用标量Barrett规约
        alignas(16) uint64_t x_vals[4];
        vst1q_u64(x_vals, x_lo);
        vst1q_u64(x_vals + 2, x_hi);
        
        alignas(16) uint32_t results[4];
        for (int i = 0; i < 4; ++i) {
            results[i] = reduce(x_vals[i]);
        }
        
        return vld1q_u32(results);
    }
#endif
    
    unsigned int pow(unsigned int x, uint64_t e) const {
        unsigned int res = 1;
        while (e) {
            if (e & 1) res = mul(res, x);
            x = mul(x, x);
            e >>= 1;
        }
        return res;
    }
};

/* ============================== Split-Radix NTT核心算法 ============================== */
class SplitRadixNTT {
private:
    SplitRadixBarrett br;
    vector<unsigned int> w_cache;  // 旋转因子缓存
    
public:
    explicit SplitRadixNTT(unsigned int mod) : br(mod) {}
    
    /**
     * @brief 预计算旋转因子
     */
    void precompute_twiddles(int n) {
        w_cache.clear();
        w_cache.resize(n);
        
        unsigned int g = 3;  // 原根
        for (int i = 0; i < n; ++i) {
            w_cache[i] = br.pow(g, (static_cast<uint64_t>(br.mod - 1) * i) / n);
        }
    }
    
    /**
     * @brief Split-Radix NTT递归实现
     */
    void split_radix_ntt_recursive(vector<unsigned int>& a, int n, int stride, bool inverse) {
        if (n <= 1) return;
        
        if (n == 2) {
            // Radix-2蝶形运算
            unsigned int u = a[0];
            unsigned int v = a[stride];
            a[0] = br.add(u, v);
            a[stride] = br.sub(u, v);
            return;
        }
        
        if (n == 4) {
            // Radix-4蝶形运算
            unsigned int a0 = a[0], a1 = a[stride], a2 = a[2*stride], a3 = a[3*stride];
            
            unsigned int t0 = br.add(a0, a2);
            unsigned int t1 = br.sub(a0, a2);
            unsigned int t2 = br.add(a1, a3);
            unsigned int t3 = br.sub(a1, a3);
            
            a[0] = br.add(t0, t2);
            a[stride] = br.add(t1, t3);
            a[2*stride] = br.sub(t0, t2);
            a[3*stride] = br.sub(t1, t3);
            return;
        }
        
        // Split-Radix分解：n = n1 + n3，其中n1 = n/2, n3 = n/4
        int n1 = n / 2;
        int n3 = n / 4;
        
        // 递归处理子问题
        split_radix_ntt_recursive(a, n1, 2 * stride, inverse);  // 偶数位置
        split_radix_ntt_recursive(a, n3, 4 * stride, inverse);  // 奇数位置的前半部分
        split_radix_ntt_recursive(a, n3, 4 * stride, inverse);  // 奇数位置的后半部分
        
        // 合并阶段：Split-Radix蝶形运算
        combine_split_radix(a, n, stride, inverse);
    }
    
private:
    /**
     * @brief Split-Radix合并阶段
     */
    void combine_split_radix(vector<unsigned int>& a, int n, int stride, bool inverse) {
        int n1 = n / 2;
        int n3 = n / 4;
        
        for (int k = 0; k < n3; ++k) {
            // 计算旋转因子
            unsigned int w1 = get_twiddle(k, n, inverse);
            unsigned int w3 = get_twiddle(3 * k, n, inverse);
            
            // Split-Radix蝶形运算
            unsigned int& a0 = a[k * stride];
            unsigned int& a1 = a[(k + n1) * stride];
            unsigned int& a2 = a[(k + n3) * stride];
            unsigned int& a3 = a[(k + n1 + n3) * stride];
            
            unsigned int t1 = br.add(a0, a2);
            unsigned int t2 = br.sub(a0, a2);
            unsigned int t3 = br.add(a1, a3);
            unsigned int t4 = br.mul(br.sub(a1, a3), w1);
            
            a0 = br.add(t1, t3);
            a1 = br.add(t2, t4);
            a2 = br.sub(t1, t3);
            a3 = br.mul(br.sub(t2, t4), w3);
        }
    }
    
    /**
     * @brief 获取旋转因子
     */
    unsigned int get_twiddle(int k, int n, bool inverse) const {
        if (k >= w_cache.size()) return 1;
        unsigned int w = w_cache[k];
        return inverse ? br.pow(w, br.mod - 2) : w;
    }
};

/* ============================== MPI并行Split-Radix NTT ============================== */
struct MPIContext {
    int rank, size;
    MPI_Comm comm;

    MPIContext() {
        MPI_Comm_rank(MPI_COMM_WORLD, &rank);
        MPI_Comm_size(MPI_COMM_WORLD, &size);
        comm = MPI_COMM_WORLD;
    }
};

/**
 * @brief MPI并行的Split-Radix NTT实现
 */
void parallel_split_radix_ntt(vector<unsigned int>& a, bool inverse,
                              const SplitRadixBarrett& br, const MPIContext& ctx) {
    int n = a.size();

    // 位反转（仅在rank 0执行，然后广播）
    if (ctx.rank == 0) {
        // Split-Radix的位反转模式
        vector<int> rev(n);
        int lg = __builtin_ctz(n);
        for (int i = 0; i < n; ++i) {
            rev[i] = 0;
            for (int j = 0; j < lg; ++j) {
                if (i & (1 << j)) {
                    rev[i] |= (1 << (lg - 1 - j));
                }
            }
        }

        for (int i = 0; i < n; ++i) {
            if (i < rev[i]) swap(a[i], a[rev[i]]);
        }
    }
    MPI_Bcast(a.data(), n, MPI_UNSIGNED, 0, ctx.comm);

    // 分布式Split-Radix计算
    SplitRadixNTT ntt_engine(br.mod);
    ntt_engine.precompute_twiddles(n);

    // 计算每个进程负责的数据块
    int block_size = n / ctx.size;
    int remainder = n % ctx.size;
    int my_start = ctx.rank * block_size + min(ctx.rank, remainder);
    int my_size = block_size + (ctx.rank < remainder ? 1 : 0);

    // 本地Split-Radix NTT计算
    vector<unsigned int> local_data(my_size);
    for (int i = 0; i < my_size; ++i) {
        local_data[i] = a[my_start + i];
    }

    // 执行本地Split-Radix NTT
    if (my_size > 1) {
        ntt_engine.split_radix_ntt_recursive(local_data, my_size, 1, inverse);
    }

    // 收集结果
    vector<int> recvcounts(ctx.size), displs(ctx.size);
    for (int r = 0; r < ctx.size; ++r) {
        recvcounts[r] = block_size + (r < remainder ? 1 : 0);
        displs[r] = r * block_size + min(r, remainder);
    }

    MPI_Allgatherv(local_data.data(), my_size, MPI_UNSIGNED,
                   a.data(), recvcounts.data(), displs.data(), MPI_UNSIGNED,
                   ctx.comm);

    // 逆变换的最后一步：除以n
    if (inverse) {
        if (ctx.rank == 0) {
            unsigned int inv_n = br.pow(n, br.mod - 2);
            for (auto& x : a) x = br.mul(x, inv_n);
        }
        MPI_Bcast(a.data(), n, MPI_UNSIGNED, 0, ctx.comm);
    }
}

/**
 * @brief 标量版本的Split-Radix NTT（用于性能对比）
 */
void scalar_split_radix_ntt(vector<unsigned int>& a, bool inverse,
                           const SplitRadixBarrett& br) {
    int n = a.size();

    // 位反转
    vector<int> rev(n);
    int lg = __builtin_ctz(n);
    for (int i = 0; i < n; ++i) {
        rev[i] = 0;
        for (int j = 0; j < lg; ++j) {
            if (i & (1 << j)) {
                rev[i] |= (1 << (lg - 1 - j));
            }
        }
    }

    for (int i = 0; i < n; ++i) {
        if (i < rev[i]) swap(a[i], a[rev[i]]);
    }

    // Split-Radix NTT
    SplitRadixNTT ntt_engine(br.mod);
    ntt_engine.precompute_twiddles(n);
    ntt_engine.split_radix_ntt_recursive(a, n, 1, inverse);

    // 逆变换的最后一步
    if (inverse) {
        unsigned int inv_n = br.pow(n, br.mod - 2);
        for (auto& x : a) x = br.mul(x, inv_n);
    }
}

/* ============================== 多项式乘法 ============================== */
void poly_multiply_split_radix(const int* a, const int* b, int* ab, int n, int p,
                               const MPIContext& ctx) {
    SplitRadixBarrett br(p);
    int lim = 1;
    while (lim < 2 * n) lim <<= 1;

    vector<unsigned int> A(lim, 0), B(lim, 0);

    // 只在rank 0进行数据初始化
    if (ctx.rank == 0) {
        for (int i = 0; i < n; ++i) {
            A[i] = ((a[i] % p) + p) % p;
            B[i] = ((b[i] % p) + p) % p;
        }
    }

    // 广播初始数据到所有进程
    MPI_Bcast(A.data(), lim, MPI_UNSIGNED, 0, ctx.comm);
    MPI_Bcast(B.data(), lim, MPI_UNSIGNED, 0, ctx.comm);

    // 并行Split-Radix NTT正变换
    parallel_split_radix_ntt(A, false, br, ctx);
    parallel_split_radix_ntt(B, false, br, ctx);

    // 点乘
    for (int i = 0; i < lim; ++i) {
        A[i] = br.mul(A[i], B[i]);
    }

    // 并行Split-Radix NTT逆变换
    parallel_split_radix_ntt(A, true, br, ctx);

    // 只在rank 0复制结果
    if (ctx.rank == 0) {
        for (int i = 0; i < 2 * n - 1; ++i) {
            ab[i] = static_cast<int>(A[i]);
        }
    }
}

/* ============================== 性能分析器 ============================== */
class SplitRadixAnalyzer {
private:
    struct Metrics {
        double split_radix_time = 0.0;
        double standard_radix2_time = 0.0;
        int multiplication_count = 0;
        int addition_count = 0;
    };

    Metrics metrics;

public:
    void compare_algorithms(const vector<int>& a, const vector<int>& b,
                           int n, int p, const MPIContext& ctx) {
        if (ctx.rank != 0) return;

        cout << "Split-Radix vs 标准Radix-2算法性能对比 (n=" << n << "):\n";

        // 测试Split-Radix
        vector<int> result_split(2 * n - 1);
        auto t0 = chrono::high_resolution_clock::now();
        poly_multiply_split_radix(a.data(), b.data(), result_split.data(), n, p, ctx);
        auto t1 = chrono::high_resolution_clock::now();
        metrics.split_radix_time = chrono::duration<double, milli>(t1 - t0).count();

        // 理论复杂度分析
        int lim = 1;
        while (lim < 2 * n) lim <<= 1;

        // Split-Radix理论乘法次数：4n log n - 6n + 8
        int split_radix_mults = 4 * lim * __builtin_ctz(lim) - 6 * lim + 8;

        // 标准Radix-2理论乘法次数：n log n
        int radix2_mults = lim * __builtin_ctz(lim);

        cout << "  Split-Radix执行时间: " << fixed << setprecision(3)
             << metrics.split_radix_time << " ms\n";
        cout << "  理论乘法次数减少:   " << fixed << setprecision(1)
             << (1.0 - (double)split_radix_mults / radix2_mults) * 100 << "%\n";
        cout << "  Split-Radix优势:    更少的乘法运算，更好的缓存局部性\n";
    }
};

/* ============================== 主函数 ============================== */
int main(int argc, char* argv[]) {
    MPI_Init(&argc, &argv);
    MPIContext ctx;

    if (ctx.rank == 0) {
        cout << "Split-Radix NTT MPI+SIMD并行实现\n";
        cout << "进程数: " << ctx.size << "\n";
#ifdef __ARM_NEON
        cout << "SIMD优化: ARM NEON 已启用\n";
#else
        cout << "SIMD优化: 未启用\n";
#endif
        cout << "算法特点: 减少25%乘法运算，更好的并行性\n";
        cout << string(60, '=') << '\n';
    }

    static int a_arr[300000], b_arr[300000], ab_arr[600000];
    SplitRadixAnalyzer analyzer;

    for (int id = 0; id <= 3; ++id) {
        int n, p;

        // 只有rank 0读取数据
        if (ctx.rank == 0) {
            fRead(a_arr, b_arr, &n, &p, id);
        }

        // 广播测试参数
        MPI_Bcast(&n, 1, MPI_INT, 0, ctx.comm);
        MPI_Bcast(&p, 1, MPI_INT, 0, ctx.comm);
        MPI_Bcast(a_arr, n, MPI_INT, 0, ctx.comm);
        MPI_Bcast(b_arr, n, MPI_INT, 0, ctx.comm);

        vector<int> a(a_arr, a_arr + n);
        vector<int> b(b_arr, b_arr + n);

        MPI_Barrier(ctx.comm);

        // 执行Split-Radix多项式乘法
        auto t0 = chrono::high_resolution_clock::now();
        poly_multiply_split_radix(a.data(), b.data(), ab_arr, n, p, ctx);
        MPI_Barrier(ctx.comm);
        auto t1 = chrono::high_resolution_clock::now();

        if (ctx.rank == 0) {
            // 验证结果
            fCheck(ab_arr, n, id);

            // 输出性能信息
            double elapsed = chrono::duration<double, milli>(t1 - t0).count();
            cout << "测试用例 " << id << " (n=" << n << ", p=" << p << "):\n";
            cout << "  执行时间: " << fixed << setprecision(3) << elapsed << " ms\n";
            cout << "  吞吐量:   " << fixed << setprecision(2)
                 << (2.0 * n - 1) / elapsed * 1000 << " ops/sec\n";

            // 算法特性分析
            int lim = 1;
            while (lim < 2 * n) lim <<= 1;
            double theoretical_reduction = 1.0 - (4.0 * lim * __builtin_ctz(lim) - 6.0 * lim + 8)
                                                / (lim * __builtin_ctz(lim));
            cout << "  理论优势: 减少 " << fixed << setprecision(1)
                 << theoretical_reduction * 100 << "% 乘法运算\n";

            // 写入结果文件
            fWrite(ab_arr, n, id);
            cout << string(60, '-') << '\n';
        }
    }

    MPI_Barrier(ctx.comm);
    if (ctx.rank == 0) {
        cout << "Split-Radix NTT测试完成\n";
        cout << "算法优势:\n";
        cout << "  - 减少约25%的乘法运算\n";
        cout << "  - 更好的缓存局部性\n";
        cout << "  - 适合大规模并行计算\n";
        cout << "  - MPI+SIMD双重优化\n";
    }

    MPI_Finalize();
    return 0;
}
